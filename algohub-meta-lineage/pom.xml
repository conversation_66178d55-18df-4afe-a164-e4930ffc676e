<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>galaxy-talos-client</artifactId>
    <groupId>com.xiaomi.infra.galaxy</groupId>
    <version>2.5-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xiaomi.algohub</groupId>
  <artifactId>algohub-meta-lineage</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <properties>
    <scala.version>2.10.4</scala.version>
    <scala.binary.version>2.10</scala.binary.version>
    <spark.version>1.6.1</spark.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
    </dependency>
    <!-- <dependency>
      <groupId>com.xiaomi.data.recommender</groupId>
      <artifactId>push-pipeline-model</artifactId>
      <version>0.1-SNAPSHOT</version>
    </dependency> -->
    <dependency>
      <groupId>com.xiaomi.growth</groupId>
      <artifactId>algohub-common</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>bcpkix-jdk15on</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
          <!-- Spring Boot 启动器/测试 -->
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-test</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-cache</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-aop</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-validation</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-freemarker</artifactId></exclusion>
        
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.14</version>
    </dependency>

    <!-- Jackson Core -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.13.0</version>
    </dependency>
    <!-- Jackson Databind -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.0</version>
    </dependency>
    <!-- Jackson Annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.13.0</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.infra.galaxy</groupId>
      <artifactId>galaxy-lcs-common</artifactId>
      <version>${galaxy-lcs-common.version}</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.infra.galaxy</groupId>
      <artifactId>galaxy-talos-sdk</artifactId>
      <version>2.7.0.2</version>
    </dependency>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-library</artifactId>
      <version>${scala.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-streaming_${scala.binary.version}</artifactId>
      <version>${spark.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.cloud.streaming</groupId>
      <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommon</artifactId>
      <version>THRIFT.1.1753081985</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.cloud.streaming</groupId>
      <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommonSim</artifactId>
      <version>THRIFT.2.1753175699</version>
    </dependency>
    <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>4.3.0</version>
    </dependency>
    <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>3.5.9</version>
    </dependency>
  </dependencies>

  <distributionManagement>
    <repository>
      <id>central</id>
      <name>maven-release-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>maven-snapshot-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
    </snapshotRepository>
  </distributionManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.10.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
            <execution>
            <phase>package</phase>
            <goals>
                <goal>shade</goal>
            </goals>
            <configuration>
                <minimizeJar>true</minimizeJar>
                <createDependencyReducedPom>false</createDependencyReducedPom>
                <shadedArtifactAttached>true</shadedArtifactAttached>
                <shadedClassifierName>jar-with-dependencies</shadedClassifierName>
                <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                    <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
                </transformer>
                </transformers>
            </configuration>
            </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>