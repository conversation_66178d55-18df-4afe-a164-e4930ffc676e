package com.xiaomi.algohub.meta.lineage.dsldepdata;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.KeeperException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ZkParser {

    /**
     * 连接zk
     * @param connectString
     * @return
     */
    public static CuratorFramework connect(String connectString) {
        //连接zk
        CuratorFramework client = CuratorFrameworkFactory.builder()
                .connectString(connectString.trim())
                .retryPolicy(new ExponentialBackoffRetry(1000, 3))
                .sessionTimeoutMs(30_000)
                .connectionTimeoutMs(5_000)
                .build();
        client.start();
        
        try {
            boolean ok = client.blockUntilConnected(15, java.util.concurrent.TimeUnit.SECONDS);
            if (!ok) {
              throw new RuntimeException(String.format("连接 ZK 超时: %s", connectString));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(String.format("等待连接被中断: %s", connectString), e);
        }
        return client;
    }


    /**
     * 读取zk节点数据
     * @param client
     * @param path
     * @return
     */
    public static String readDataAsString(CuratorFramework client, String path) {
        if (client == null) {
            throw new IllegalArgumentException("client 不能为空");
        }
        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("path 不能为空");
        }
        try {
            byte[] bytes = client.getData().forPath(path.trim());
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (KeeperException.NoNodeException e) {
            throw new RuntimeException(String.format("ZK 节点不存在: %s", path), e);
        } catch (Exception e) {
            throw new RuntimeException(String.format("读取 ZK 节点失败: %s", path), e);
        }
    }

    /**
     * 解析xml数据
     * @param xml
     * @return
     */
    public static List<Map<String, String>> parsePropertyGroups(String xml) {
        if (xml == null || xml.trim().isEmpty()) {
            throw new IllegalArgumentException("xml 内容不能为空");
        }
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(false);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setExpandEntityReferences(false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new java.io.ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
            doc.getDocumentElement().normalize();

            NodeList groups = doc.getElementsByTagName("propertyGroup");
            return IntStream.range(0, groups.getLength())
                    .mapToObj(groups::item)
                    .filter(Objects::nonNull)
                    .filter(node -> node.getNodeType() == Node.ELEMENT_NODE)
                    .map(node -> (Element) node)
                    .map(ZkParser::elementToMap)
                    .collect(Collectors.toList());
        } catch (org.xml.sax.SAXParseException e) {
            throw new RuntimeException(String.format("XML 解析错误(行:%d, 列:%d): %s",
                    e.getLineNumber(), e.getColumnNumber(), e.getMessage()), e);
        } catch (Exception e) {
            throw new RuntimeException(String.format("XML 解析失败: %s", e.getMessage()), e);
        }
    }

    /**
     * 将xml节点转换为map
     * @param groupElement
     * @return
     */
    private static Map<String, String> elementToMap(Element groupElement) {
        Map<String, String> groupMap = new LinkedHashMap<>();
        NodeList childNodes = groupElement.getChildNodes();
        IntStream.range(0, childNodes.getLength())
                .mapToObj(childNodes::item)
                .filter(Objects::nonNull)
                .filter(n -> n.getNodeType() == Node.ELEMENT_NODE)
                .map(n -> (Element) n)
                .forEach(el -> {
                    String key = el.getTagName();
                    String value = el.getTextContent() == null ? "" : el.getTextContent().trim();
                    groupMap.put(key, value);
                });
        return groupMap;
    }

    /**
     * 获取并解析xml数据
     * @param connectString
     * @param xmlPath
     * @return
     */
    public static List<Map<String, String>> fetchAndParse(String connectString, String xmlPath) {
        CuratorFramework client = null;
        try {
            client = connect(connectString);
            String xml = readDataAsString(client, xmlPath);
            return parsePropertyGroups(xml);
        } finally {
            if (client != null) {
                client.close();
            }
        }
    }

    /**
     * 主函数
     * @param args
     */
    public static void main(String[] args) {

        String zkConnect = "c3srv.zk.hadoop.srv:11000";
        String xmlPath = "/data/services/recommend/config_data/ctr-score/model_config_app_store.xml";
        

        CuratorFramework client = null;
        try {
            client = connect(zkConnect);

            List<Map<String, String>> propertyGroupList = fetchAndParse(zkConnect, xmlPath);
            System.out.println(String.format("解析结果，共 %d 个 propertyGroup：", propertyGroupList.size()));
            IntStream.range(0, propertyGroupList.size())
                    .forEach(i -> System.out.println(String.format("[%d] %s", i, propertyGroupList.get(i))));
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        } finally {
            if (client != null) {
                client.close();
            }
        }
    }
}